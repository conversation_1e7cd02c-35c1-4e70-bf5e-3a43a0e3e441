import os
import re
from typing import <PERSON><PERSON>

def verify_video_file(video_path: str) -> Tuple[bool, str]:
    if not os.path.exists(video_path):
        print(f"Error: Video file {video_path} does not exist")
        return False, video_path
    
    file_size = os.path.getsize(video_path)
    if file_size < 10240:
        print(f"Error: Video file is too small ({file_size} bytes)")
        return False, video_path
    
    if not video_path.lower().endswith(('.mp4', '.mov', '.avi', '.wmv')):
        print(f"Warning: Video file has an unsupported extension: {video_path}")
        try:
            new_path = os.path.splitext(video_path)[0] + '.mp4'
            if os.path.exists(new_path):
                print(f"Using existing MP4 file: {new_path}")
                video_path = new_path
            else:
                os.rename(video_path, new_path)
                video_path = new_path
                print(f"Renamed video file to: {video_path}")
        except Exception as e:
            print(f"Error renaming video file: {e}")
            return False, video_path
    
    try:
        import cv2
        cap = cv2.VideoCapture(video_path)
        
        if not cap.isOpened():
            print(f"Warning: OpenCV could not open the video file")
        else:
            fps = cap.get(cv2.CAP_PROP_FPS)
            frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
            duration = frame_count / fps if fps > 0 else 0
            width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
            height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
            
            cap.release()
            
            print(f"Video verified by OpenCV: {os.path.basename(video_path)}")
            print(f"  Resolution: {width}x{height}, Duration: {duration:.1f}s, FPS: {fps:.1f}")
    except Exception as e:
        print(f"Warning: OpenCV could not verify the video file: {e}")
    
    print(f"Video verified: {os.path.basename(video_path)}, Size: {file_size/1024:.1f}KB")
    return True, video_path

def ensure_mp4_format(video_path: str) -> str:
    if video_path.lower().endswith('.mp4'):
        return video_path
    
    mp4_path = os.path.splitext(video_path)[0] + '.mp4'
    
    try:
        import cv2
        cap = cv2.VideoCapture(video_path)
        if not cap.isOpened():
            print(f"Could not open video file: {video_path}")
            return video_path
            
        width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
        height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
        fps = cap.get(cv2.CAP_PROP_FPS)
        if fps <= 0:
            fps = 24
            
        fourcc = cv2.VideoWriter_fourcc(*'mp4v')
        out = cv2.VideoWriter(mp4_path, fourcc, fps, (width, height))
        
        if not out.isOpened():
            print(f"Could not create output video: {mp4_path}")
            return video_path
            
        print(f"Converting video to MP4 format: {mp4_path}")
        frame_count = 0
        while True:
            ret, frame = cap.read()
            if not ret:
                break
                
            out.write(frame)
            frame_count += 1
            
            if frame_count % 100 == 0:
                print(f"Processed {frame_count} frames")
                
        cap.release()
        out.release()
        
        print(f"Successfully converted video to MP4 format: {mp4_path}")
        return mp4_path
        
    except Exception as e:
        print(f"Error converting video to MP4: {e}")
        return video_path

def cleanup_files(video_path: str) -> None:
    try:
        if os.path.exists(video_path):
            os.remove(video_path)
            print(f"Removed video file: {video_path}")
        else:
            base_path = os.path.splitext(video_path)[0]
            for ext in ['.mp4', '.jpg', '.png', '.avi', '.mov', '.wmv']:
                file_to_check = base_path + ext
                if os.path.exists(file_to_check):
                    os.remove(file_to_check)
                    print(f"Removed related file: {file_to_check}")
    except Exception as e:
        print(f"Warning: Could not remove video file: {e}")

def sanitize_text(text):
    if not text:
        return ""
        
    if not isinstance(text, str):
        text = str(text)
        
    text = text.replace('\r', ' ')
    text = text.replace('\0', '')
    
    text = re.sub(r'[\x00-\x08\x0B-\x0C\x0E-\x1F\x7F]', '', text)
    
    if len(text) > 4800:
        text = text[:4800] + "..."
        
    return text
