import requests
import json

def get_top_scripts(count=10, exclude_universal=False):
    """
    Fetches the top most popular scripts from ScriptBlox based on view count.

    Args:
        count (int): Number of scripts to fetch (max 20)
        exclude_universal (bool): Whether to exclude universal scripts

    Returns:
        list: List of script dictionaries containing title, game, and other info
    """
    # API endpoint
    url = "https://scriptblox.com/api/script/fetch"

    # Parameters for the request
    params = {
        "max": 20,  # Fetch maximum allowed to ensure we get enough specific game scripts
        "sortBy": "views",
        "order": "desc"
    }

    # Add parameter to exclude universal scripts if requested
    if exclude_universal:
        params["universal"] = 0

    try:
        # Make the request
        response = requests.get(url, params=params)
        response.raise_for_status()  # Raise exception for HTTP errors

        # Parse the JSON response
        data = response.json()

        # Get the scripts
        scripts = data["result"]["scripts"]

        # If we're excluding universal scripts, double-check each script
        if exclude_universal:
            scripts = [script for script in scripts if not script.get("isUniversal", False)]

        # Limit to requested count
        return scripts[:count]

    except requests.exceptions.RequestException as e:
        print(f"Error making request: {e}")
        return []
    except (KeyError, json.JSONDecodeError) as e:
        print(f"Error parsing response: {e}")
        return []

def display_scripts(scripts):
    """
    Displays the scripts in a formatted way.

    Args:
        scripts (list): List of script dictionaries
    """
    if not scripts:
        print("No scripts found.")
        return

    print("\n{:<5} {:<50} {:<30} {:<10}".format("Rank", "Script Title", "Game", "Views"))
    print("-" * 95)

    for i, script in enumerate(scripts, 1):
        title = script.get("title", "Unknown Title")
        game_name = script.get("game", {}).get("name", "Universal")
        views = script.get("views", 0)

        # Truncate long titles
        if len(title) > 47:
            title = title[:47] + "..."

        # Truncate long game names
        if len(game_name) > 27:
            game_name = game_name[:27] + "..."

        print("{:<5} {:<50} {:<30} {:<10}".format(i, title, game_name, views))

def main():
    print("Fetching top 10 most popular game-specific scripts from ScriptBlox...")
    scripts = get_top_scripts(10, exclude_universal=True)
    display_scripts(scripts)

if __name__ == "__main__":
    main()
