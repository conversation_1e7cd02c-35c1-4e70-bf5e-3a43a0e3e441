# ScriptBlox YouTube Poster

Automatically creates and uploads YouTube videos showcasing popular game-specific scripts from ScriptBlox.

## Setup

1. Install requirements:
   ```
   pip install -r requirements.txt
   ```

2. Set up YouTube API credentials:
   - Download OAuth 2.0 credentials as `client_secrets.json`
   - For multiple accounts, use `client_secrets_accountX.json` in the `credentials` folder

## Usage

```
# Process all accounts
python main.py

# Use a specific account
python main.py --account account1

# Specify number of scripts per account
python main.py --scripts 15

# List configured accounts
python main.py --list-accounts
```

## Configuration

Edit `config.py` to customize:
- YouTube accounts
- Number of scripts per account
- Linkvertise and LootLabs credentials
