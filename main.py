import os
import time
import random
import argparse
import json

from config import Config
from api.scriptblox import get_popular_game_scripts
from api.youtube import upload_to_youtube
from utils.video_creator import create_video
from utils.file_utils import cleanup_files

def check_credentials_setup():
    os.makedirs(Config.CREDENTIALS_DIR, exist_ok=True)
    
    if not os.path.exists('client_secrets.json'):
        print("\nWARNING: client_secrets.json not found!")
        print("You need to create OAuth 2.0 credentials in Google Cloud Console.")
        print("\nInstructions:")
        print("1. Go to https://console.cloud.google.com/")
        print("2. Create a project and enable YouTube Data API v3")
        print("3. Create OAuth 2.0 credentials (Desktop app type)")
        print("4. Download the credentials and save as 'client_secrets.json'")
        
        sample = {
            "installed": *************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
        }
        
        with open('client_secrets_SAMPLE.json', 'w') as f:
            json.dump(sample, f, indent=4)
        
        print(f"\nA sample file 'client_secrets_SAMPLE.json' has been created.")
        print(f"Replace the placeholder values with your actual credentials.")
        print(f"Then rename it to 'client_secrets.json'")
        print(f"\nPress Enter to continue or Ctrl+C to exit...")
        input()

def process_scripts_for_account(account_name, start_index, count):
    print(f"\nFetching scripts for account: {account_name} (scripts {start_index+1}-{start_index+count})")
    
    total_scripts_needed = start_index + count
    scripts = get_popular_game_scripts(total_scripts_needed)
    
    if not scripts or len(scripts) <= start_index:
        print(f"Not enough scripts found for account {account_name}. Exiting.")
        return
    
    account_scripts = scripts[start_index:start_index+count]
    print(f"Found {len(account_scripts)} scripts for account {account_name}. Creating and uploading videos...")
    
    for i, script in enumerate(account_scripts, 1):
        print(f"\nProcessing script {i}/{len(account_scripts)} for {account_name}: {script.get('title')}")
        
        video_path = create_video(script)
        if not video_path:
            print("Failed to create video. Skipping to next script.")
            continue
        
        video_id = upload_to_youtube(video_path, script, account_name)
        if video_id:
            print(f"Video uploaded successfully to {account_name}: https://www.youtube.com/watch?v={video_id}")
        else:
            print(f"Failed to upload video to YouTube account {account_name}.")
        
        cleanup_files(video_path)
        
        if i < len(account_scripts):
            delay = 5
            print(f"Waiting {delay} seconds before next upload...")
            time.sleep(delay)
    
    print(f"\nAll videos for account {account_name} have been processed.")

def main():
    Config.ensure_dirs()
    check_credentials_setup()
    
    parser = argparse.ArgumentParser(description='Upload ScriptBlox scripts to YouTube')
    parser.add_argument('--account', '-a', type=str, help='Specific YouTube account to use')
    parser.add_argument('--scripts', '-s', type=int, default=Config.SCRIPTS_PER_ACCOUNT, 
                        help=f'Number of scripts per account (default: {Config.SCRIPTS_PER_ACCOUNT})')
    parser.add_argument('--list-accounts', '-l', action='store_true', help='List configured YouTube accounts')
    args = parser.parse_args()
    
    if args.list_accounts:
        print("\nConfigured YouTube accounts:")
        for i, account in enumerate(Config.YOUTUBE_ACCOUNTS, 1):
            print(f"{i}. {account}")
        return
    
    if args.account:
        if args.account not in Config.YOUTUBE_ACCOUNTS:
            print(f"Error: Account '{args.account}' not found in configuration.")
            print("Available accounts: " + ", ".join(Config.YOUTUBE_ACCOUNTS))
            return
        
        process_scripts_for_account(args.account, 0, args.scripts)
    else:
        print(f"Processing {args.scripts} scripts for each of {len(Config.YOUTUBE_ACCOUNTS)} accounts...")
        
        for i, account in enumerate(Config.YOUTUBE_ACCOUNTS):
            start_index = i * args.scripts
            process_scripts_for_account(account, start_index, args.scripts)
    
    print("\nAll accounts have been processed.")

if __name__ == "__main__":
    main()
