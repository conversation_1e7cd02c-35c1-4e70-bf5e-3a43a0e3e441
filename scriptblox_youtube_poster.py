import requests
import json
import os
import time
import random
import base64
import argparse
import re
from typing import Optional, List
from datetime import datetime
import cv2
import numpy as np
from google_auth_oauthlib.flow import InstalledAppFlow
from googleapiclient.discovery import build
from googleapiclient.http import MediaFileUpload
from google.oauth2.credentials import Credentials
from google.auth.transport.requests import Request

# Configuration
class Config:
    # Linkvertise configuration
    LINKVERTISE_USER_ID: int = 1216780

    # LootLabs configuration
    LOOTLABS_API_URL: str = "https://be.lootlabs.gg/api/lootlabs/content_locker"
    LOOTLABS_API_TOKEN: Optional[str] = "8e4062d769921232dd76fbeaa687d5d3e50f9f088f29302c870cd76c2b3b42a4"

    # Request configuration
    REQUEST_TIMEOUT: int = 30
    MAX_RETRIES: int = 3
    RETRY_DELAY: int = 2

    # YouTube account configuration
    # List of account names - you can add as many as you want
    YOUTUBE_ACCOUNTS: List[str] = ["account1", "account2", "account3"]

    # Number of scripts per account
    SCRIPTS_PER_ACCOUNT: int = 10

# ScriptBlox API functions
def get_popular_game_scripts(count=10):
    """
    Fetches the top most popular game-specific scripts from ScriptBlox.

    Args:
        count (int): Number of scripts to fetch

    Returns:
        list: List of script dictionaries with detailed information
    """
    # API endpoint for fetching scripts
    url = "https://scriptblox.com/api/script/fetch"

    # Parameters for the request
    params = {
        "max": 20,  # Fetch maximum allowed to ensure we have enough after filtering
        "sortBy": "views",
        "order": "desc",
        "universal": 0  # Exclude universal scripts
    }

    try:
        # Make the request
        response = requests.get(url, params=params)
        response.raise_for_status()

        # Parse the JSON response
        data = response.json()

        # Get the scripts and filter out any universal scripts that might have slipped through
        scripts = [script for script in data["result"]["scripts"]
                  if not script.get("isUniversal", False)]

        # For each script, get detailed information
        detailed_scripts = []
        for script in scripts[:count]:
            detailed_script = get_script_details(script["slug"])
            if detailed_script:
                detailed_scripts.append(detailed_script)
                # Add a small delay to avoid rate limiting
                time.sleep(1)

        return detailed_scripts

    except Exception as e:
        print(f"Error fetching popular scripts: {e}")
        return []

def get_script_details(slug):
    """
    Fetches detailed information about a specific script.

    Args:
        slug (str): The script's slug

    Returns:
        dict: Detailed script information
    """
    url = f"https://scriptblox.com/api/script/{slug}"

    try:
        response = requests.get(url)
        response.raise_for_status()
        data = response.json()
        return data.get("script")

    except Exception as e:
        print(f"Error fetching script details for {slug}: {e}")
        return None

# Video creation functions
def create_video(script, output_dir="videos"):
    """
    Creates a 5-minute video using the script's image.

    Args:
        script (dict): Script information
        output_dir (str): Directory to save the video

    Returns:
        str: Path to the created video file
    """
    # Create output directory if it doesn't exist
    os.makedirs(output_dir, exist_ok=True)

    # Get the script image URL
    image_url = script.get("image")
    if not image_url:
        # Use game image as fallback
        image_url = script.get("game", {}).get("imageUrl")

    # Fix relative URLs by adding the base URL
    if image_url and image_url.startswith('/'):
        image_url = f"https://scriptblox.com{image_url}"

    if not image_url:
        print(f"No image found for script: {script.get('title')}")
        return None

    # Download the image
    image_path = os.path.join(output_dir, f"temp_image_{script.get('_id')}.jpg")  # Always use .jpg extension
    try:
        # Add headers to mimic a browser request
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.9',
            'Referer': 'https://scriptblox.com/',
            'sec-ch-ua': '" Not A;Brand";v="99", "Chromium";v="91"',
            'sec-ch-ua-mobile': '?0'
        }

        image_response = requests.get(image_url, headers=headers)
        image_response.raise_for_status()
        with open(image_path, 'wb') as f:
            f.write(image_response.content)
    except Exception as e:
        print(f"Error downloading image: {e}")

        # Try to use a default image as fallback
        try:
            # Create a simple colored image with text using PIL
            from PIL import Image, ImageDraw, ImageFont

            # Create a colored background
            img = Image.new('RGB', (1280, 720), color=(33, 33, 33))
            draw = ImageDraw.Draw(img)

            # Try to load a font, use default if not available
            try:
                font_title = ImageFont.truetype("arial.ttf", 60)
                font_game = ImageFont.truetype("arial.ttf", 40)
            except IOError:
                font_title = ImageFont.load_default()
                font_game = ImageFont.load_default()

            # Add script title
            title = script.get('title', 'Unknown Script')
            draw.text((640, 260), title, fill=(255, 255, 255), font=font_title, anchor="mm")

            # Add game name
            game_name = script.get('game', {}).get('name', 'Unknown Game')
            draw.text((640, 360), f"Game: {game_name}", fill=(200, 200, 200), font=font_game, anchor="mm")

            # Add ScriptBlox logo text
            draw.text((640, 460), "ScriptBlox", fill=(66, 135, 245), font=font_title, anchor="mm")

            # Make sure the image path has a .jpg extension
            if not image_path.lower().endswith('.jpg'):
                image_path = os.path.splitext(image_path)[0] + '.jpg'

            # Save the image
            img.save(image_path, format='JPEG')
            print(f"Created fallback image for {title}: {image_path}")
            return image_path

        except Exception as fallback_error:
            print(f"Failed to create fallback image: {fallback_error}")
            return None

    # Create a 5-minute video from the image
    # Ensure the video has a .mp4 extension
    video_path = os.path.join(output_dir, f"{script.get('_id')}.mp4")
    # Double-check that the path ends with .mp4
    if not video_path.lower().endswith('.mp4'):
        video_path = os.path.splitext(video_path)[0] + '.mp4'
    try:
        # Create a video using OpenCV (cv2)
        print("Creating video using OpenCV...")

        # Read the image
        img = cv2.imread(image_path)
        if img is None:
            raise Exception(f"Failed to read image: {image_path}")

        # Resize image to 1280x720 (720p)
        img = cv2.resize(img, (1280, 720))

        # Get image dimensions
        height, width, _ = img.shape  # _ ignores the channels

        # Define video parameters
        fps = 24
        duration = 300  # 5 minutes in seconds
        total_frames = fps * duration

        # Create video writer
        fourcc = cv2.VideoWriter_fourcc(*'mp4v')  # MP4 codec
        out = cv2.VideoWriter(video_path, fourcc, fps, (width, height))

        if not out.isOpened():
            raise Exception("Could not open video writer")

        # Write the same image for all frames
        # To save time, we'll write fewer frames but with longer duration between them
        # This creates the same 5-minute video but processes faster
        sample_rate = 4  # Write every 4th frame
        for i in range(0, total_frames, sample_rate):
            if i % (fps * 10) == 0:  # Print progress every 10 seconds
                print(f"Writing video: {i/total_frames*100:.1f}% complete")
            out.write(img)

        # Release the video writer
        out.release()
        print("Successfully created video with OpenCV")

        # Verify the file was created and has a reasonable size
        if os.path.exists(video_path) and os.path.getsize(video_path) < 10240:  # Less than 10KB
            raise Exception("Generated video file is too small, likely not a valid video")

        # Remove the temporary image file
        os.remove(image_path)

        return video_path
    except Exception as e:
        print(f"Error creating video: {e}")
        if os.path.exists(image_path):
            os.remove(image_path)
        return None

# URL and link generation functions
def generate_linkvertise_link(url: str) -> str:
    """
    Generate a Linkvertise monetized URL

    Args:
        url (str): The URL to monetize

    Returns:
        str: The monetized Linkvertise URL
    """
    encoded_url = base64.b64encode(url.encode()).decode().rstrip("=")
    random_number = round(random.random() * 1000, 6)
    return f"https://link-to.net/{Config.LINKVERTISE_USER_ID}/{random_number}/dynamic/?r={encoded_url}"

def generate_lootlabs_link(url: str) -> Optional[str]:
    """
    Generate a LootLabs monetized URL

    Args:
        url (str): The URL to monetize

    Returns:
        Optional[str]: The monetized LootLabs URL, or None if generation failed
    """
    if not Config.LOOTLABS_API_TOKEN:
        return None

    for attempt in range(Config.MAX_RETRIES):
        try:
            headers = {
                "Authorization": f"Bearer {Config.LOOTLABS_API_TOKEN}",
                "Content-Type": "application/json"
            }

            response = requests.post(
                Config.LOOTLABS_API_URL,
                json = {
                    "title": "Script Link",
                    "url": url,
                    "tier_id": 1,
                    "number_of_tasks": 3,
                    "theme": 1
                },
                headers=headers,
                timeout=Config.REQUEST_TIMEOUT
            )

            if response.status_code == 200:
                data = response.json()
                if data.get('type') == 'created' and isinstance(data.get('message'), list):
                    first_item = data['message'][0]
                    if isinstance(first_item, dict) and 'loot_url' in first_item:
                        return first_item['loot_url']

            time.sleep(Config.RETRY_DELAY * (attempt + 1))
        except requests.RequestException as e:
            print(f"Error generating LootLabs link (attempt {attempt + 1}): {str(e)}")
            time.sleep(Config.RETRY_DELAY * (attempt + 1))

    return None

def shorten_url(url: str) -> str:
    """
    Shorten a URL using TinyURL

    Args:
        url (str): The URL to shorten

    Returns:
        str: The shortened URL, or the original URL if shortening failed
    """
    if not url:
        return ""

    try:
        response = requests.get(
            f"https://tinyurl.com/api-create.php?url={url}",
            timeout=Config.REQUEST_TIMEOUT
        )
        return response.text if response.status_code == 200 else url
    except requests.RequestException:
        return url

# YouTube API functions
def get_youtube_credentials(account_name="account1"):
    """
    Gets or refreshes YouTube API credentials for a specific account.

    Args:
        account_name (str): The name of the YouTube account to use

    Returns:
        Credentials: YouTube API credentials for the specified account
    """
    # Create credentials directory if it doesn't exist
    os.makedirs('credentials', exist_ok=True)

    # Token file path for this account
    token_file = os.path.join('credentials', f'token_{account_name}.json')

    creds = None
    # The token file stores the user's access and refresh tokens
    if os.path.exists(token_file):
        try:
            with open(token_file, 'r') as token:
                creds = Credentials.from_authorized_user_info(json.load(token))
        except (json.JSONDecodeError, ValueError) as e:
            print(f"Error loading credentials for {account_name}: {e}")

    # If there are no valid credentials available, let the user log in
    if not creds or not creds.valid:
        if creds and creds.expired and creds.refresh_token:
            creds.refresh(Request())
        else:
            # Client secrets file path - can be account-specific or shared
            client_secrets_file = os.path.join('credentials', f'client_secrets_{account_name}.json')
            if not os.path.exists(client_secrets_file):
                client_secrets_file = 'client_secrets.json'  # Fall back to default

            if not os.path.exists(client_secrets_file):
                print(f"\nERROR: Client secrets file not found for account {account_name}")
                print(f"Please download your OAuth 2.0 credentials from Google Cloud Console")
                print(f"and save them as '{client_secrets_file}'")
                print(f"\nInstructions:")
                print(f"1. Go to https://console.cloud.google.com/")
                print(f"2. Create a project and enable YouTube Data API v3")
                print(f"3. Create OAuth 2.0 credentials")
                print(f"4. Download the credentials and save as '{client_secrets_file}'")
                print(f"5. Run this script again")
                raise FileNotFoundError(f"Client secrets file not found for account {account_name}")

            print(f"\nAuthorizing YouTube account: {account_name}")
            print("A browser window will open. Please log in with the correct YouTube account.")
            print(f"After authorization, credentials will be saved to {token_file}\n")

            flow = InstalledAppFlow.from_client_secrets_file(
                client_secrets_file,
                ['https://www.googleapis.com/auth/youtube.upload',
                 'https://www.googleapis.com/auth/youtube.force-ssl']
            )
            creds = flow.run_local_server(port=0)

        # Save the credentials for the next run
        with open(token_file, 'w') as token:
            token.write(creds.to_json())

    return creds

def ensure_mp4_format(video_path):
    """
    Ensures the video is in MP4 format by re-encoding it if necessary.

    Args:
        video_path (str): Path to the video file

    Returns:
        str: Path to the MP4 video file (may be the same or a new file)
    """
    # If already an MP4 file with the right extension, return as is
    if video_path.lower().endswith('.mp4'):
        return video_path

    # Create a new path with .mp4 extension
    mp4_path = os.path.splitext(video_path)[0] + '.mp4'

    try:
        # Open the source video
        cap = cv2.VideoCapture(video_path)
        if not cap.isOpened():
            print(f"Could not open video file: {video_path}")
            return video_path  # Return original path, will fail later

        # Get video properties
        width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
        height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
        fps = cap.get(cv2.CAP_PROP_FPS)
        if fps <= 0:
            fps = 24  # Default to 24 fps if can't determine

        # Create video writer for MP4
        fourcc = cv2.VideoWriter_fourcc(*'mp4v')
        out = cv2.VideoWriter(mp4_path, fourcc, fps, (width, height))

        if not out.isOpened():
            print(f"Could not create output video: {mp4_path}")
            return video_path  # Return original path, will fail later

        # Process the video frame by frame
        print(f"Converting video to MP4 format: {mp4_path}")
        frame_count = 0
        while True:
            ret, frame = cap.read()
            if not ret:
                break

            out.write(frame)
            frame_count += 1

            if frame_count % 100 == 0:
                print(f"Processed {frame_count} frames")

        # Release resources
        cap.release()
        out.release()

        print(f"Successfully converted video to MP4 format: {mp4_path}")
        return mp4_path

    except Exception as e:
        print(f"Error converting video to MP4: {e}")
        return video_path  # Return original path, will fail later

def verify_video_file(video_path):
    """
    Verify that the video file is valid and in a format YouTube accepts.

    Args:
        video_path (str): Path to the video file

    Returns:
        tuple: (bool, str) - (is_valid, new_path)
            is_valid: True if the file is valid, False otherwise
            new_path: The path to the verified file (may be different from input if renamed)
    """
    if not os.path.exists(video_path):
        print(f"Error: Video file {video_path} does not exist")
        return False, video_path

    # Check file size (should be at least 10KB for a valid video)
    file_size = os.path.getsize(video_path)
    if file_size < 10240:  # 10KB
        print(f"Error: Video file is too small ({file_size} bytes)")
        return False, video_path

    # Check file extension and fix it if needed
    if not video_path.lower().endswith(('.mp4', '.mov', '.avi', '.wmv')):
        print(f"Warning: Video file has an unsupported extension: {video_path}")
        # Try to rename the file to have a .mp4 extension
        try:
            new_path = os.path.splitext(video_path)[0] + '.mp4'
            if os.path.exists(new_path):
                # If the MP4 file already exists, use it
                print(f"Using existing MP4 file: {new_path}")
                video_path = new_path
            else:
                # Rename the file
                os.rename(video_path, new_path)
                video_path = new_path
                print(f"Renamed video file to: {video_path}")
        except Exception as e:
            print(f"Error renaming video file: {e}")
            return False, video_path

    # Try to check the file using OpenCV
    try:
        # Open the video file
        cap = cv2.VideoCapture(video_path)

        # Check if video opened successfully
        if not cap.isOpened():
            print(f"Warning: OpenCV could not open the video file")
            # Continue anyway with basic checks
        else:
            # Get video properties
            fps = cap.get(cv2.CAP_PROP_FPS)
            frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
            duration = frame_count / fps if fps > 0 else 0
            width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
            height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))

            # Release the video capture
            cap.release()

            print(f"Video verified by OpenCV: {os.path.basename(video_path)}")
            print(f"  Resolution: {width}x{height}, Duration: {duration:.1f}s, FPS: {fps:.1f}")
    except Exception as e:
        print(f"Warning: OpenCV could not verify the video file: {e}")
        # Continue with basic checks

    # If we got here, the file exists, has a valid extension, and has a reasonable size
    print(f"Video verified: {os.path.basename(video_path)}, Size: {file_size/1024:.1f}KB")
    return True, video_path

def upload_to_youtube(video_path, script, account_name="account1"):
    """
    Uploads a video to YouTube with metadata from the script.

    Args:
        video_path (str): Path to the video file
        script (dict): Script information
        account_name (str): The name of the YouTube account to use

    Returns:
        str: YouTube video ID if successful, None otherwise
    """
    try:
        # Verify the video file before uploading
        is_valid, verified_path = verify_video_file(video_path)
        if not is_valid:
            return None

        # Use the verified path (which may have been renamed)
        video_path = verified_path

        # Ensure the video is in MP4 format
        video_path = ensure_mp4_format(video_path)
        print(f"Using video path: {video_path}")

        # Final check to make sure the file exists
        if not os.path.exists(video_path):
            print(f"Error: Final video file {video_path} does not exist")
            return None

        # Get YouTube API credentials for the specified account
        credentials = get_youtube_credentials(account_name)
        youtube = build('youtube', 'v3', credentials=credentials)

        # Define a function to sanitize text for YouTube
        def sanitize_text(text):
            if not text:
                return ""

            # Convert to string if not already
            if not isinstance(text, str):
                text = str(text)

            # Replace problematic characters
            text = text.replace('\r', ' ')
            text = text.replace('\0', '')

            # Replace other potentially problematic characters
            # Remove control characters except newlines and tabs
            text = re.sub(r'[\x00-\x08\x0B-\x0C\x0E-\x1F\x7F]', '', text)

            # Limit length to avoid YouTube's description limit
            if len(text) > 4800:  # YouTube limit is 5000, leave some margin
                text = text[:4800] + "..."

            return text

        # Prepare video metadata
        # Sanitize the title
        script_title = sanitize_text(script.get('title', ''))
        game_name = sanitize_text(script.get('game', {}).get('name', ''))

        # Create a valid title (YouTube limit is 100 characters)
        title = f"{script_title} - {game_name} Script Showcase"
        if len(title) > 100:
            # Truncate to fit YouTube's limit
            title = title[:97] + "..."

        # Start with basic info
        description = f"Script Showcase for {sanitize_text(script.get('title'))} - {sanitize_text(script.get('game', {}).get('name'))}\n\n"

        # Add features if available
        features = script.get('features')
        if features:
            # Sanitize and truncate features if too long
            features = sanitize_text(features)
            if features:
                description += "Features:\n" + features + "\n\n"

        # Add metadata
        description += f"Views on ScriptBlox: {script.get('views', 0)}\n"
        created_at = script.get('createdAt', '')
        if created_at:
            # Ensure createdAt is a string and sanitize
            created_at = sanitize_text(str(created_at))
            description += f"Created: {created_at}\n"

        script_type = script.get('scriptType', '')
        if script_type:
            # Ensure scriptType is a string and sanitize
            script_type = sanitize_text(str(script_type))
            description += f"Script Type: {script_type}\n"

        # Add verification status
        if script.get('verified'):
            description += "This script is verified on ScriptBlox\n"

        # Add patched warning
        if script.get('isPatched'):
            description += "This script may be patched\n"

        # Add a disclaimer
        description += "\n\nDisclaimer: This video is for educational purposes only."

        # Get tags from script
        tags = []

        # Sanitize script tags
        for tag in script.get('tags', []):
            # Ensure tag is a string and sanitize it
            if tag and isinstance(tag, str):
                sanitized_tag = sanitize_text(tag)
                if sanitized_tag:
                    tags.append(sanitized_tag)

        # Add some default tags
        default_tags = ["roblox script", "roblox exploit", "script showcase",
                        sanitize_text(script.get('game', {}).get('name', "")), "scriptblox"]

        # Add default tags that aren't empty
        for tag in default_tags:
            if tag:
                tags.append(tag)

        # Remove duplicates and limit to 500 characters total (YouTube limit)
        tags = list(set(tags))

        # Remove any invalid tags (empty or too long)
        tags = [tag for tag in tags if tag and len(tag) <= 100]  # YouTube tag limit is 100 chars

        # Check total length
        tags_str = ",".join(tags)
        if len(tags_str) > 500:
            # Trim tags to stay under limit
            while tags and len(",".join(tags)) > 500:
                tags.pop()

        # Prepare the upload request
        request_body = {
            'snippet': {
                'title': title,
                'description': description,
                'tags': tags,
                'categoryId': '20'  # Gaming category
            },
            'status': {
                'privacyStatus': 'public',
                'selfDeclaredMadeForKids': False
            }
        }

        # Upload the video
        print(f"Uploading video file: {video_path}")
        media = MediaFileUpload(video_path, mimetype='video/mp4', chunksize=-1, resumable=True)
        upload_request = youtube.videos().insert(
            part=",".join(request_body.keys()),
            body=request_body,
            media_body=media
        )

        print(f"Uploading video: {title}")
        response = upload_request.execute()
        video_id = response.get('id')

        # Add comments with links
        if video_id:
            # Create the script URL
            script_url = f"https://scriptblox.com/script/{script.get('slug')}"

            # Generate monetized links
            linkvertise_link = generate_linkvertise_link(script_url)
            lootlabs_link = generate_lootlabs_link(script_url) or ""

            # Use the key link from ScriptBlox if available
            if script.get('keyLink'):
                linkvertise_link = generate_linkvertise_link(script.get('keyLink'))

            # Shorten the links
            short_lootlabs_link = shorten_url(lootlabs_link) if lootlabs_link else ""
            short_linkvertise_link = shorten_url(linkvertise_link)

            # Post comments with the shortened links
            if short_lootlabs_link:
                youtube.commentThreads().insert(
                    part="snippet",
                    body={
                        "snippet": {
                            "videoId": video_id,
                            "topLevelComment": {
                                "snippet": {
                                    "textOriginal": f"🔑 Get the script key here: {short_lootlabs_link}"
                                }
                            }
                        }
                    }
                ).execute()

            youtube.commentThreads().insert(
                part="snippet",
                body={
                    "snippet": {
                        "videoId": video_id,
                        "topLevelComment": {
                            "snippet": {
                                "textOriginal": f"⚡ Alternative link for script: {short_linkvertise_link}"
                            }
                        }
                    }
                }
            ).execute()

            print(f"Successfully uploaded video: {video_id}")
            return video_id

    except Exception as e:
        print(f"Error uploading to YouTube: {e}")

    return None

def process_scripts_for_account(account_name, start_index, count):
    """
    Process a batch of scripts for a specific YouTube account.

    Args:
        account_name (str): The name of the YouTube account to use
        start_index (int): The starting index for scripts (0-based)
        count (int): The number of scripts to process
    """
    print(f"\nFetching scripts for account: {account_name} (scripts {start_index+1}-{start_index+count})")

    # Fetch enough scripts to cover all accounts
    total_scripts_needed = start_index + count
    scripts = get_popular_game_scripts(total_scripts_needed)

    if not scripts or len(scripts) <= start_index:
        print(f"Not enough scripts found for account {account_name}. Exiting.")
        return

    # Get the subset of scripts for this account
    account_scripts = scripts[start_index:start_index+count]
    print(f"Found {len(account_scripts)} scripts for account {account_name}. Creating and uploading videos...")

    for i, script in enumerate(account_scripts, 1):
        print(f"\nProcessing script {i}/{len(account_scripts)} for {account_name}: {script.get('title')}")

        # Create video
        video_path = create_video(script)
        if not video_path:
            print("Failed to create video. Skipping to next script.")
            continue

        # Upload to YouTube using the specified account
        video_id = upload_to_youtube(video_path, script, account_name)
        if video_id:
            print(f"Video uploaded successfully to {account_name}: https://www.youtube.com/watch?v={video_id}")
        else:
            print(f"Failed to upload video to YouTube account {account_name}.")

        # Remove the video file to save space
        try:
            if os.path.exists(video_path):
                os.remove(video_path)
                print(f"Removed video file: {video_path}")
            else:
                # Try to find and remove any related files
                base_path = os.path.splitext(video_path)[0]
                for ext in ['.mp4', '.jpg', '.png', '.avi', '.mov', '.wmv']:
                    file_to_check = base_path + ext
                    if os.path.exists(file_to_check):
                        os.remove(file_to_check)
                        print(f"Removed related file: {file_to_check}")
        except Exception as e:
            print(f"Warning: Could not remove video file: {e}")

        # Add a delay between uploads to avoid rate limiting
        if i < len(account_scripts):
            delay = random.randint(3, 5)  # 1-3 minutes
            print(f"Waiting {delay} seconds before next upload...")
            time.sleep(delay)

    print(f"\nAll videos for account {account_name} have been processed.")

def check_credentials_setup():
    """
    Check if client_secrets.json exists and create a sample one if it doesn't.
    Also creates the credentials directory.
    """
    # Create credentials directory
    os.makedirs('credentials', exist_ok=True)

    # Check if client_secrets.json exists
    if not os.path.exists('client_secrets.json'):
        print("\nWARNING: client_secrets.json not found!")
        print("You need to create OAuth 2.0 credentials in Google Cloud Console.")
        print("\nInstructions:")
        print("1. Go to https://console.cloud.google.com/")
        print("2. Create a project and enable YouTube Data API v3")
        print("3. Create OAuth 2.0 credentials (Desktop app type)")
        print("4. Download the credentials and save as 'client_secrets.json'")

        # Create a sample client_secrets.json file with instructions
        sample = {
            "installed": *************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
        }

        # Save the sample file
        with open('client_secrets_SAMPLE.json', 'w') as f:
            json.dump(sample, f, indent=4)

        print(f"\nA sample file 'client_secrets_SAMPLE.json' has been created.")
        print(f"Replace the placeholder values with your actual credentials.")
        print(f"Then rename it to 'client_secrets.json'")
        print(f"\nPress Enter to continue or Ctrl+C to exit...")
        input()

def main():
    """
    Main function to fetch scripts and upload videos to YouTube.
    """
    # Check if credentials are set up
    check_credentials_setup()

    # Parse command-line arguments
    parser = argparse.ArgumentParser(description='Upload ScriptBlox scripts to YouTube')
    parser.add_argument('--account', '-a', type=str, help='Specific YouTube account to use')
    parser.add_argument('--scripts', '-s', type=int, default=Config.SCRIPTS_PER_ACCOUNT,
                        help=f'Number of scripts per account (default: {Config.SCRIPTS_PER_ACCOUNT})')
    parser.add_argument('--list-accounts', '-l', action='store_true', help='List configured YouTube accounts')
    args = parser.parse_args()

    # List accounts if requested
    if args.list_accounts:
        print("\nConfigured YouTube accounts:")
        for i, account in enumerate(Config.YOUTUBE_ACCOUNTS, 1):
            print(f"{i}. {account}")
        return

    # If a specific account is specified, only process that account
    if args.account:
        if args.account not in Config.YOUTUBE_ACCOUNTS:
            print(f"Error: Account '{args.account}' not found in configuration.")
            print("Available accounts: " + ", ".join(Config.YOUTUBE_ACCOUNTS))
            return

        # Process scripts for the specified account (starting from the beginning)
        process_scripts_for_account(args.account, 0, args.scripts)
    else:
        # Process scripts for all accounts
        print(f"Processing {args.scripts} scripts for each of {len(Config.YOUTUBE_ACCOUNTS)} accounts...")

        for i, account in enumerate(Config.YOUTUBE_ACCOUNTS):
            # Calculate the starting index for this account
            start_index = i * args.scripts
            process_scripts_for_account(account, start_index, args.scripts)

    print("\nAll accounts have been processed.")

if __name__ == "__main__":
    main()

