import base64
import random
import time
import requests
from typing import Optional
from config import Config

def generate_linkvertise_link(url: str) -> str:
    encoded_url = base64.b64encode(url.encode()).decode().rstrip("=")
    random_number = round(random.random() * 1000, 6)
    return f"https://link-to.net/{Config.LINKVERTISE_USER_ID}/{random_number}/dynamic/?r={encoded_url}"

def generate_lootlabs_link(url: str) -> Optional[str]:
    if not Config.LOOTLABS_API_TOKEN:
        return None
        
    for attempt in range(Config.MAX_RETRIES):
        try:
            headers = {
                "Authorization": f"Bearer {Config.LOOTLABS_API_TOKEN}",
                "Content-Type": "application/json"
            }
            
            response = requests.post(
                Config.LOOTLABS_API_URL,
                json = {
                    "title": "Script Link",
                    "url": url,
                    "tier_id": 1,
                    "number_of_tasks": 3,
                    "theme": 1
                },
                headers=headers,
                timeout=Config.REQUEST_TIMEOUT
            )
            
            if response.status_code == 200:
                data = response.json()
                if data.get('type') == 'created' and isinstance(data.get('message'), list):
                    first_item = data['message'][0]
                    if isinstance(first_item, dict) and 'loot_url' in first_item:
                        return first_item['loot_url']
                        
            time.sleep(Config.RETRY_DELAY * (attempt + 1))
        except requests.RequestException as e:
            print(f"Error generating LootLabs link (attempt {attempt + 1}): {str(e)}")
            time.sleep(Config.RETRY_DELAY * (attempt + 1))
            
    return None

def shorten_url(url: str) -> str:
    if not url:
        return ""
        
    try:
        response = requests.get(
            f"https://tinyurl.com/api-create.php?url={url}",
            timeout=Config.REQUEST_TIMEOUT
        )
        return response.text if response.status_code == 200 else url
    except requests.RequestException:
        return url
