import requests
import time
from typing import List, Dict, Any, Optional
from config import Config

def get_popular_game_scripts(count: int = 10) -> List[Dict[str, Any]]:
    url = "https://scriptblox.com/api/script/fetch"
    
    params = {
        "max": 20,
        "sortBy": "views",
        "order": "desc",
        "universal": 0
    }
    
    try:
        response = requests.get(url, params=params)
        response.raise_for_status()
        
        data = response.json()
        
        scripts = [script for script in data["result"]["scripts"] 
                  if not script.get("isUniversal", False)]
        
        detailed_scripts = []
        for script in scripts[:count]:
            detailed_script = get_script_details(script["slug"])
            if detailed_script:
                detailed_scripts.append(detailed_script)
                time.sleep(1)
        
        return detailed_scripts
    
    except Exception as e:
        print(f"Error fetching popular scripts: {e}")
        return []

def get_script_details(slug: str) -> Optional[Dict[str, Any]]:
    url = f"https://scriptblox.com/api/script/{slug}"
    
    try:
        response = requests.get(url)
        response.raise_for_status()
        data = response.json()
        return data.get("script")
    
    except Exception as e:
        print(f"Error fetching script details for {slug}: {e}")
        return None
