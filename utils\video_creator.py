import os
import requests
import cv2
import numpy as np
from typing import Dict, Any, Optional
from PIL import Image, ImageDraw, ImageFont
from config import Config

def create_video(script: Dict[str, Any], output_dir: str = Config.OUTPUT_DIR) -> Optional[str]:
    os.makedirs(output_dir, exist_ok=True)
    
    image_url = script.get("image")
    if not image_url:
        image_url = script.get("game", {}).get("imageUrl")
    
    if image_url and image_url.startswith('/'):
        image_url = f"https://scriptblox.com{image_url}"

    if not image_url:
        print(f"No image found for script: {script.get('title')}")
        return None
    
    image_path = os.path.join(output_dir, f"temp_image_{script.get('_id')}.jpg")
    try:
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.9',
            'Referer': 'https://scriptblox.com/',
            'sec-ch-ua': '" Not A;Brand";v="99", "Chromium";v="91"',
            'sec-ch-ua-mobile': '?0'
        }
        
        image_response = requests.get(image_url, headers=headers)
        image_response.raise_for_status()
        with open(image_path, 'wb') as f:
            f.write(image_response.content)
    except Exception as e:
        print(f"Error downloading image: {e}")
        
        try:
            img = Image.new('RGB', (1280, 720), color=(33, 33, 33))
            draw = ImageDraw.Draw(img)
            
            try:
                font_title = ImageFont.truetype("arial.ttf", 60)
                font_game = ImageFont.truetype("arial.ttf", 40)
            except IOError:
                font_title = ImageFont.load_default()
                font_game = ImageFont.load_default()
            
            title = script.get('title', 'Unknown Script')
            draw.text((640, 260), title, fill=(255, 255, 255), font=font_title, anchor="mm")
            
            game_name = script.get('game', {}).get('name', 'Unknown Game')
            draw.text((640, 360), f"Game: {game_name}", fill=(200, 200, 200), font=font_game, anchor="mm")
            
            draw.text((640, 460), "ScriptBlox", fill=(66, 135, 245), font=font_title, anchor="mm")
            
            if not image_path.lower().endswith('.jpg'):
                image_path = os.path.splitext(image_path)[0] + '.jpg'
                
            img.save(image_path, format='JPEG')
            print(f"Created fallback image for {title}: {image_path}")
            return image_path
            
        except Exception as fallback_error:
            print(f"Failed to create fallback image: {fallback_error}")
            return None
    
    video_path = os.path.join(output_dir, f"{script.get('_id')}.mp4")
    if not video_path.lower().endswith('.mp4'):
        video_path = os.path.splitext(video_path)[0] + '.mp4'
    
    try:
        print("Creating video using OpenCV...")
        
        img = cv2.imread(image_path)
        if img is None:
            raise Exception(f"Failed to read image: {image_path}")
        
        img = cv2.resize(img, (1280, 720))
        
        height, width, _ = img.shape
        
        fps = 24
        duration = 300
        total_frames = fps * duration
        
        fourcc = cv2.VideoWriter_fourcc(*'mp4v')
        out = cv2.VideoWriter(video_path, fourcc, fps, (width, height))
        
        if not out.isOpened():
            raise Exception("Could not open video writer")
        
        sample_rate = 4
        for i in range(0, total_frames, sample_rate):
            if i % (fps * 10) == 0:
                print(f"Writing video: {i/total_frames*100:.1f}% complete")
            out.write(img)
        
        out.release()
        print("Successfully created video with OpenCV")
        
        if os.path.exists(video_path) and os.path.getsize(video_path) < 10240:
            raise Exception("Generated video file is too small, likely not a valid video")
            
        os.remove(image_path)
        return video_path
        
    except Exception as e:
        print(f"Error creating video: {e}")
        if os.path.exists(image_path):
            os.remove(image_path)
        return None
