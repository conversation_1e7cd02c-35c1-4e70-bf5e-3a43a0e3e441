import os
import json
import re
from typing import Dict, Any, Optional, List
from google_auth_oauthlib.flow import InstalledAppFlow
from googleapiclient.discovery import build
from googleapiclient.http import MediaFileUpload
from google.oauth2.credentials import Credentials
from google.auth.transport.requests import Request

from config import Config
from utils.file_utils import verify_video_file, ensure_mp4_format, sanitize_text
from utils.link_generator import generate_linkvertise_link, generate_lootlabs_link, shorten_url

def get_youtube_credentials(account_name="account1"):
    os.makedirs(Config.CREDENTIALS_DIR, exist_ok=True)
    
    token_file = os.path.join(Config.CREDENTIALS_DIR, f'token_{account_name}.json')
    
    creds = None
    if os.path.exists(token_file):
        try:
            with open(token_file, 'r') as token:
                creds = Credentials.from_authorized_user_info(json.load(token))
        except (json.JSONDecodeError, ValueError) as e:
            print(f"Error loading credentials for {account_name}: {e}")
    
    if not creds or not creds.valid:
        if creds and creds.expired and creds.refresh_token:
            creds.refresh(Request())
        else:
            client_secrets_file = os.path.join(Config.CREDENTIALS_DIR, f'client_secrets_{account_name}.json')
            if not os.path.exists(client_secrets_file):
                client_secrets_file = 'client_secrets.json'
            
            if not os.path.exists(client_secrets_file):
                print(f"\nERROR: Client secrets file not found for account {account_name}")
                print(f"Please download your OAuth 2.0 credentials from Google Cloud Console")
                print(f"and save them as '{client_secrets_file}'")
                print(f"\nInstructions:")
                print(f"1. Go to https://console.cloud.google.com/")
                print(f"2. Create a project and enable YouTube Data API v3")
                print(f"3. Create OAuth 2.0 credentials")
                print(f"4. Download the credentials and save as '{client_secrets_file}'")
                print(f"5. Run this script again")
                raise FileNotFoundError(f"Client secrets file not found for account {account_name}")
            
            print(f"\nAuthorizing YouTube account: {account_name}")
            print("A browser window will open. Please log in with the correct YouTube account.")
            print(f"After authorization, credentials will be saved to {token_file}\n")
            
            flow = InstalledAppFlow.from_client_secrets_file(
                client_secrets_file,
                ['https://www.googleapis.com/auth/youtube.upload',
                 'https://www.googleapis.com/auth/youtube.force-ssl']
            )
            creds = flow.run_local_server(port=0)
        
        with open(token_file, 'w') as token:
            token.write(creds.to_json())
    
    return creds

def upload_to_youtube(video_path: str, script: Dict[str, Any], account_name: str = "account1") -> Optional[str]:
    try:
        is_valid, verified_path = verify_video_file(video_path)
        if not is_valid:
            return None
            
        video_path = verified_path
        
        video_path = ensure_mp4_format(video_path)
        print(f"Using video path: {video_path}")
        
        if not os.path.exists(video_path):
            print(f"Error: Final video file {video_path} does not exist")
            return None

        credentials = get_youtube_credentials(account_name)
        youtube = build('youtube', 'v3', credentials=credentials)
        
        script_title = sanitize_text(script.get('title', ''))
        game_name = sanitize_text(script.get('game', {}).get('name', ''))
        
        title = f"{script_title} - {game_name} Script Showcase"
        if len(title) > 100:
            title = title[:97] + "..."

        description = f"Script Showcase for {sanitize_text(script.get('title'))} - {sanitize_text(script.get('game', {}).get('name'))}\n\n"

        features = script.get('features')
        if features:
            features = sanitize_text(features)
            if features:
                description += "Features:\n" + features + "\n\n"

        description += f"Views on ScriptBlox: {script.get('views', 0)}\n"
        created_at = script.get('createdAt', '')
        if created_at:
            created_at = sanitize_text(str(created_at))
            description += f"Created: {created_at}\n"
            
        script_type = script.get('scriptType', '')
        if script_type:
            script_type = sanitize_text(str(script_type))
            description += f"Script Type: {script_type}\n"

        if script.get('verified'):
            description += "This script is verified on ScriptBlox\n"

        if script.get('isPatched'):
            description += "This script may be patched\n"
            
        description += "\n\nDisclaimer: This video is for educational purposes only."

        tags = []
        
        for tag in script.get('tags', []):
            if tag and isinstance(tag, str):
                sanitized_tag = sanitize_text(tag)
                if sanitized_tag:
                    tags.append(sanitized_tag)
        
        default_tags = ["roblox script", "roblox exploit", "script showcase", 
                        sanitize_text(script.get('game', {}).get('name', "")), "scriptblox"]
        
        for tag in default_tags:
            if tag:
                tags.append(tag)
        
        tags = list(set(tags))
        
        tags = [tag for tag in tags if tag and len(tag) <= 100]
        
        tags_str = ",".join(tags)
        if len(tags_str) > 500:
            while tags and len(",".join(tags)) > 500:
                tags.pop()
        
        request_body = {
            'snippet': {
                'title': title,
                'description': description,
                'tags': tags,
                'categoryId': '20'
            },
            'status': {
                'privacyStatus': 'public',
                'selfDeclaredMadeForKids': False
            }
        }
        
        print(f"Uploading video file: {video_path}")
        media = MediaFileUpload(video_path, mimetype='video/mp4', chunksize=-1, resumable=True)
        upload_request = youtube.videos().insert(
            part=",".join(request_body.keys()),
            body=request_body,
            media_body=media
        )
        
        print(f"Uploading video: {title}")
        response = upload_request.execute()
        video_id = response.get('id')
        
        if video_id:
            script_url = f"https://scriptblox.com/script/{script.get('slug')}"
            
            linkvertise_link = generate_linkvertise_link(script_url)
            lootlabs_link = generate_lootlabs_link(script_url) or ""
            
            if script.get('keyLink'):
                linkvertise_link = generate_linkvertise_link(script.get('keyLink'))
            
            short_lootlabs_link = shorten_url(lootlabs_link) if lootlabs_link else ""
            short_linkvertise_link = shorten_url(linkvertise_link)
            
            if short_lootlabs_link:
                youtube.commentThreads().insert(
                    part="snippet",
                    body={
                        "snippet": {
                            "videoId": video_id,
                            "topLevelComment": {
                                "snippet": {
                                    "textOriginal": f"🔑 Get the script key here: {short_lootlabs_link}"
                                }
                            }
                        }
                    }
                ).execute()
            
            youtube.commentThreads().insert(
                part="snippet",
                body={
                    "snippet": {
                        "videoId": video_id,
                        "topLevelComment": {
                            "snippet": {
                                "textOriginal": f"⚡ Alternative link for script: {short_linkvertise_link}"
                            }
                        }
                    }
                }
            ).execute()
            
            print(f"Successfully uploaded video: {video_id}")
            return video_id
    
    except Exception as e:
        print(f"Error uploading to YouTube: {e}")
    
    return None
