import os
from typing import Optional, List

class Config:
    LINKVERTISE_USER_ID: int = 1216780
    
    LOOTLABS_API_URL: str = "https://be.lootlabs.gg/api/lootlabs/content_locker"
    LOOTLABS_API_TOKEN: Optional[str] = "8e4062d769921232dd76fbeaa687d5d3e50f9f088f29302c870cd76c2b3b42a4"
    
    REQUEST_TIMEOUT: int = 30
    MAX_RETRIES: int = 3
    RETRY_DELAY: int = 2
    
    YOUTUBE_ACCOUNTS: List[str] = ["account1", "account2", "account3"]
    SCRIPTS_PER_ACCOUNT: int = 10
    
    OUTPUT_DIR: str = "videos"
    CREDENTIALS_DIR: str = "credentials"
    
    @classmethod
    def ensure_dirs(cls):
        os.makedirs(cls.OUTPUT_DIR, exist_ok=True)
        os.makedirs(cls.CREDENTIALS_DIR, exist_ok=True)
